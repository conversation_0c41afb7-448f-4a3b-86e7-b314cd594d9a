# 文件夹导出工具

## 功能描述

该工具可以根据Excel表格中的SKU列，将对应名称的文件夹及其内容从源位置复制到目标位置。

## 使用方法

1. 运行程序：`python folder_exporter.py` 或双击 `启动程序.bat`
2. 程序会弹出文件选择窗口，选择包含SKU列的Excel文件
3. 选择源文件夹（包含SKU命名的文件夹的目录）
4. 选择导出目标文件夹位置
5. 程序会自动读取Excel中的SKU列，并将源目录中对应的文件夹及其内容复制到目标位置
6. 操作完成后，程序会显示复制结果

## 要求

- Excel文件必须包含名为"SKU"的列
- SKU列中的值将用于查找源文件夹
- 源目录中必须存在与SKU对应名称的文件夹
- 程序会忽略SKU列中的空值
- 如果目标位置已存在同名文件夹，或源位置不存在对应文件夹，则不会复制，会在结果中显示错误信息

## 环境要求

- Python 3.6+
- 依赖包：
  - pandas
  - tkinter
  - openpyxl

## 安装依赖

```bash
pip install pandas openpyxl
```

tkinter通常包含在Python标准库中，无需单独安装。

## 分享给其他用户

本程序有两种分享方式：

### 方式一：分享源代码

如果对方已安装Python环境，可以直接分享整个文件夹，对方运行`启动程序.bat`会自动检查环境并安装所需依赖。

要求：
- 对方需要安装Python 3.6或更高版本
- 对方可能需要联网安装依赖包

### 方式二：分享独立可执行文件（推荐）

使用以下步骤将程序打包成独立可执行文件：

1. 在本目录下运行`打包成exe.bat`
2. 脚本会自动安装PyInstaller并打包程序
3. 打包完成后，可执行文件位于`dist`文件夹中
4. 将`dist`文件夹中的`文件夹导出工具.exe`分享给其他用户

优点：
- 对方无需安装Python和任何依赖
- 可以直接双击运行
- 更易于分享和使用

## 注意事项

- 确保对源文件夹有读取权限，对目标文件夹有写入权限
- 大文件夹的复制可能需要较长时间
- 如果源文件夹不存在或目标文件夹已存在，程序会跳过该项并显示错误信息
- 程序会生成日志文件`folder_exporter.log`，记录所有操作

## 注意事项

- 确保对目标文件夹有写入权限
- 大量SKU数据的处理可能需要一些时间
- 文件夹名称会按照Excel中的SKU值创建，不会进行特殊字符处理 