#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import shutil
from pathlib import Path
import logging
import time
import datetime


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='folder_exporter.log',
    filemode='a'
)
logger = logging.getLogger('FolderExporter')


class FolderExporter:
    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏主窗口
        self.root.title("文件夹导出工具")
        
        # 创建进度窗口
        self.progress_window = None
        self.progress_bar = None
        self.progress_label = None
        self.status_label = None
        
        # 设置窗口图标（如果有）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
    def select_excel_file(self):
        """打开文件选择对话框，让用户选择Excel文件"""
        logger.info("正在选择Excel文件...")
        file_path = filedialog.askopenfilename(
            title="请提供目标SKU",
            filetypes=[
                ("Excel文件", "*.xlsx *.xls"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            logger.info(f"已选择Excel文件: {file_path}")
        else:
            logger.info("未选择Excel文件")
        return file_path
    
    def select_source_folder(self):
        """打开文件夹选择对话框，让用户选择源文件夹"""
        logger.info("正在选择源文件夹...")
        folder_path = filedialog.askdirectory(
            title="选择源文件夹(将在此目录及其所有子目录中搜索SKU)"
        )
        if folder_path:
            logger.info(f"已选择源文件夹: {folder_path}")
        else:
            logger.info("未选择源文件夹")
        return folder_path
    
    def select_export_folder(self):
        """打开文件夹选择对话框，让用户选择导出目标文件夹"""
        logger.info("正在选择导出目标文件夹...")
        folder_path = filedialog.askdirectory(
            title="选择导出目标文件夹"
        )
        if folder_path:
            logger.info(f"已选择导出目标文件夹: {folder_path}")
        else:
            logger.info("未选择导出目标文件夹")
        return folder_path
    
    def read_sku_from_excel(self, excel_path):
        """从Excel文件中读取SKU列的数据"""
        try:
            logger.info(f"正在读取Excel文件: {excel_path}")
            # 读取Excel文件
            df = pd.read_excel(excel_path)
            
            # 检查是否存在SKU列
            if 'SKU' not in df.columns:
                error_msg = "在Excel文件中未找到'SKU'列"
                logger.error(error_msg)
                messagebox.showerror("错误", error_msg)
                return []
            
            # 获取SKU列数据并过滤掉空值
            sku_list = df['SKU'].dropna().tolist()
            
            if not sku_list:
                warning_msg = "SKU列中没有数据"
                logger.warning(warning_msg)
                messagebox.showwarning("警告", warning_msg)
            else:
                logger.info(f"成功读取到 {len(sku_list)} 个SKU")
            
            return sku_list
        
        except Exception as e:
            error_msg = f"读取Excel文件时发生错误：{str(e)}"
            logger.error(error_msg, exc_info=True)
            messagebox.showerror("错误", error_msg)
            return []
    
    def create_progress_window(self, total_folders):
        """创建进度显示窗口"""
        self.progress_window = tk.Toplevel(self.root)
        self.progress_window.title("复制进度")
        self.progress_window.geometry("400x150")
        self.progress_window.resizable(False, False)
        
        # 防止用户关闭进度窗口
        self.progress_window.protocol("WM_DELETE_WINDOW", lambda: None)
        
        # 居中显示
        self.progress_window.update_idletasks()
        width = self.progress_window.winfo_width()
        height = self.progress_window.winfo_height()
        x = (self.progress_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.progress_window.winfo_screenheight() // 2) - (height // 2)
        self.progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        # 创建标签
        tk.Label(self.progress_window, text="正在复制文件夹...", font=("Arial", 10)).pack(pady=(10, 0))
        
        # 创建进度条
        self.progress_bar = ttk.Progressbar(self.progress_window, orient="horizontal", length=350, mode="determinate")
        self.progress_bar.pack(pady=(10, 0))
        
        # 创建进度标签
        self.progress_label = tk.Label(self.progress_window, text="0 / {}".format(total_folders), font=("Arial", 10))
        self.progress_label.pack(pady=(5, 0))
        
        # 创建状态标签
        self.status_label = tk.Label(self.progress_window, text="准备开始...", font=("Arial", 9))
        self.status_label.pack(pady=(5, 0))
        
        # 设置进度条最大值
        self.progress_bar["maximum"] = total_folders
        self.progress_bar["value"] = 0
        
        # 更新窗口
        self.progress_window.update()
    
    def update_progress(self, current, total, status_text):
        """更新进度条和状态信息"""
        if self.progress_bar:
            self.progress_bar["value"] = current
            self.progress_label.config(text=f"{current} / {total}")
            self.status_label.config(text=status_text)
            self.progress_window.update()
    
    def find_sku_folder(self, root_dir, sku):
        """递归搜索包含指定SKU的文件夹
        
        Args:
            root_dir: 搜索的根目录
            sku: 要搜索的SKU字符串
            
        Returns:
            找到的文件夹路径，如果未找到则返回None
        """
        sku = str(sku).strip()
        logger.info(f"开始搜索SKU: {sku}")
        
        # 首先检查直接匹配（传统方式）
        direct_path = os.path.join(root_dir, sku)
        if os.path.isdir(direct_path):
            logger.info(f"直接匹配成功: {direct_path}")
            return direct_path
            
        # 如果没有直接匹配，开始递归搜索
        try:
            for root, dirs, files in os.walk(root_dir):
                for dir_name in dirs:
                    # 检查完全匹配
                    if dir_name == sku:
                        full_path = os.path.join(root, dir_name)
                        logger.info(f"找到SKU文件夹: {full_path}")
                        return full_path
        except Exception as e:
            logger.error(f"搜索SKU {sku} 时发生错误: {str(e)}")
        
        logger.warning(f"未找到SKU文件夹: {sku}")
        return None
    
    def create_export_subfolder(self, export_path):
        """在导出目标文件夹中创建一个新的子文件夹用于存放所有复制的文件夹"""
        now = datetime.datetime.now()
        subfolder_name = f"导出SKU_{now.strftime('%Y%m%d_%H%M%S')}"
        subfolder_path = os.path.join(export_path, subfolder_name)
        
        try:
            os.makedirs(subfolder_path, exist_ok=True)
            logger.info(f"创建导出子文件夹: {subfolder_path}")
            return subfolder_path
        except Exception as e:
            error_msg = f"创建导出子文件夹时发生错误: {str(e)}"
            logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
            return export_path  # 如果创建失败，返回原始导出路径
    
    def copy_folders(self, source_path, export_path, sku_list):
        """查找并复制指定SKU的文件夹及其内容到目标位置"""
        copied_folders = []
        errors = []
        total_folders = len(sku_list)
        
        # 创建进度显示窗口
        self.create_progress_window(total_folders)
        
        logger.info(f"开始查找和复制文件夹，共 {total_folders} 个SKU")
        start_time = time.time()
        
        # 在导出目标文件夹中创建一个新的子文件夹用于存放所有复制的文件夹
        export_subfolder = self.create_export_subfolder(export_path)
        logger.info(f"所有SKU文件夹将被复制到: {export_subfolder}")
        
        for i, sku in enumerate(sku_list):
            try:
                # 确保SKU是字符串
                sku_str = str(sku).strip()
                if not sku_str:
                    continue
                
                # 更新进度
                status_text = f"正在查找: {sku_str}"
                self.update_progress(i, total_folders, status_text)
                logger.info(f"处理 ({i+1}/{total_folders}): {sku_str}")
                
                # 查找SKU对应的文件夹
                source_folder = self.find_sku_folder(source_path, sku_str)
                
                # 检查源文件夹是否找到
                if not source_folder:
                    error_msg = f"源文件夹 '{sku_str}' 不存在"
                    errors.append(error_msg)
                    logger.warning(error_msg)
                    continue
                
                # 目标文件夹路径 (在新创建的子文件夹中)
                target_folder = os.path.join(export_subfolder, sku_str)
                
                # 检查目标文件夹是否已存在
                if os.path.exists(target_folder):
                    error_msg = f"目标位置已存在文件夹 '{sku_str}'"
                    errors.append(error_msg)
                    logger.warning(error_msg)
                    continue
                
                # 更新状态
                self.update_progress(i, total_folders, f"正在复制: {sku_str}")
                
                # 复制文件夹及其内容
                logger.info(f"开始复制文件夹: {sku_str}")
                copy_start = time.time()
                shutil.copytree(source_folder, target_folder)
                copy_end = time.time()
                copy_time = copy_end - copy_start
                
                logger.info(f"文件夹 {sku_str} 复制完成，耗时: {copy_time:.2f}秒")
                copied_folders.append(sku_str)
                
            except Exception as e:
                error_msg = f"复制文件夹 '{sku_str}' 时发生错误：{str(e)}"
                errors.append(error_msg)
                logger.error(error_msg, exc_info=True)
        
        # 完成所有复制
        end_time = time.time()
        total_time = end_time - start_time
        logger.info(f"所有文件夹复制完成，总耗时: {total_time:.2f}秒")
        
        # 更新最终进度
        self.update_progress(total_folders, total_folders, f"完成! 总耗时: {total_time:.2f}秒")
        
        # 关闭进度窗口
        if self.progress_window:
            self.progress_window.after(2000, self.progress_window.destroy)
        
        return copied_folders, errors, export_subfolder
    
    def run(self):
        """运行程序的主函数"""
        try:
            logger.info("=== 程序启动 ===")
            logger.info(f"当前时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 选择Excel文件
            excel_path = self.select_excel_file()
            if not excel_path:
                messagebox.showinfo("提示", "未选择Excel文件，程序已取消")
                logger.info("程序取消: 未选择Excel文件")
                return
            
            # 读取SKU数据
            sku_list = self.read_sku_from_excel(excel_path)
            if not sku_list:
                logger.info("程序取消: 没有SKU数据")
                return
            
            # 选择源文件夹
            source_path = self.select_source_folder()
            if not source_path:
                messagebox.showinfo("提示", "未选择源文件夹，程序已取消")
                logger.info("程序取消: 未选择源文件夹")
                return
            
            # 选择导出目标文件夹
            export_path = self.select_export_folder()
            if not export_path:
                messagebox.showinfo("提示", "未选择导出目标文件夹，程序已取消")
                logger.info("程序取消: 未选择导出目标文件夹")
                return
            
            # 复制文件夹
            copied_folders, errors, export_subfolder = self.copy_folders(source_path, export_path, sku_list)
            
            # 显示结果
            result_message = f"成功复制 {len(copied_folders)} 个文件夹及其内容"
            result_message += f"\n导出位置: {export_subfolder}"
            if errors:
                result_message += f"\n发生 {len(errors)} 个错误:\n" + "\n".join(errors[:10])
                if len(errors) > 10:
                    result_message += f"\n... 以及 {len(errors) - 10} 个其他错误"
            
            logger.info(f"复制结果: 成功 {len(copied_folders)} 个, 失败 {len(errors)} 个")
            messagebox.showinfo("导出完成", result_message)
            
        except Exception as e:
            error_msg = f"程序运行时发生错误：{str(e)}"
            logger.error(error_msg, exc_info=True)
            messagebox.showerror("错误", error_msg)
        finally:
            logger.info("=== 程序结束 ===")
            self.root.destroy()


if __name__ == "__main__":
    exporter = FolderExporter()
    exporter.run() 